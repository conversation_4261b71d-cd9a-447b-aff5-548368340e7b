{"name": "eproc-frontend", "version": "0.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build --concurrency=100%", "build-account": "turbo run build --filter=./apps/account --filter=./apps/register", "build-govmart": "turbo run build --filter=./apps/buyer --filter=./apps/seller --filter=./apps/internal", "build-blacklist": "turbo run build --filter=./apps/blacklist", "dev": "turbo run dev", "lint": "turbo run lint", "format": "git diff --name-only --diff-filter=d HEAD | grep -E '\\.(js|jsx|ts|tsx|css|scss|md|html)$' | xargs prettier --write", "start": "turbo run start", "prepare": "node scripts/prepare.js", "test": "turbo test --filter=./apps/buyer && turbo test --filter=./apps/seller"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.5.1", "cookies-next": "^4.2.1", "husky": "^8.0.3", "prettier": "^2.8.7", "turbo": "^1.8.3", "typescript": "^4.9.5", "vercel": "^30.0.0", "wrangler": "^3.5.1", "node-gyp-build": "^4.8.0"}, "engines": {"node": ">=20.0.0"}, "packageManager": "pnpm@7.29.1", "pnpm": {"overrides": {"@cypress/code-coverage": "^3.12.35", "@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/client-preset": "^4.8.1", "@graphql-typed-document-node/core": "^3.2.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "graphql": "^16.11.0", "graphql-request": "^7.2.0", "next": "14.2.25", "tailwindcss": "^3.3.3", "typescript": "^5.2.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-feather": "^2.0.10", "eslint-config-next": "^13.5.6", "zod": "3.21.4", "@sentry/nextjs": "7.106.0", "@unleash/nextjs": "^1.4.1", "cypress": "^13.9.0", "pdfjs-dist": "4.8.69", "react-pdf": "9.2.1", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "winston": "^3.17.0", "@types/socket.io-client": "^3.0.0", "core-js": ">=3.36.0"}}}